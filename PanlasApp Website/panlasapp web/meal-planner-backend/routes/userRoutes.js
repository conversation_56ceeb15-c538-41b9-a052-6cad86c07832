const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');
const { requireTermsAcceptance, updateTermsAcceptance } = require('../middleware/termsAuth');
const { loginRateLimit } = require('../middleware/rateLimiter');
const { getRecentlyViewedMeals } = require('../controllers/userController');

// User registration routes (both /signup and /register for compatibility)
router.post('/signup', userController.register);
router.post('/register', userController.register);

// Login route with rate limiting
router.post('/login', loginRateLimit, userController.login);

// Logout route
router.post('/logout', auth, userController.logout);

// Email verification routes
router.get('/verify-email', userController.verifyEmail);
router.post('/resend-verification', userController.resendVerificationEmail);

// Password reset routes
router.post('/request-password-reset', userController.requestPasswordReset);
router.post('/reset-password', userController.resetPassword);

// OTP verification routes
router.post('/verify-otp', userController.verifyOTP);
router.post('/resend-otp', userController.resendOTP);

// Terms and conditions routes
router.get('/terms-status', auth, userController.getTermsStatus);
router.post('/accept-terms', auth, updateTermsAcceptance, userController.acceptTerms);

// Protected routes (require terms acceptance)
router.get('/profile', auth, userController.getProfile);
router.put('/profile', auth.authWithTerms, userController.updateProfile);
router.put('/change-password', auth.authWithTerms, userController.changePassword);
// router.delete('/account', auth, userController.deleteAccount);

// New routes for dietary preferences
router.get('/dietary-preferences', auth.authWithTerms, userController.getDietaryPreferences);
router.put('/dietary-preferences', auth.authWithTerms, userController.updateDietaryPreferences);

// Routes for favorite meals
router.get('/favorite-meals', auth.authWithTerms, userController.getFavoriteMeals);
router.post('/favorite-meals', auth.authWithTerms, userController.addFavoriteMeal);
router.delete('/favorite-meals/:mealId', auth.authWithTerms, userController.removeFavoriteMeal);

// Routes for favorite meal plans
router.get('/favorite-meal-plans', auth.authWithTerms, userController.getFavoriteMealPlans);
router.post('/favorite-meal-plans', auth.authWithTerms, userController.addFavoriteMealPlan);
router.delete('/favorite-meal-plans/:planId', auth.authWithTerms, userController.removeFavoriteMealPlan);


router.get('/recently-viewed-meals', auth.authWithTerms, userController.getRecentlyViewedMeals);
router.post('/recently-viewed-meals', auth.authWithTerms, userController.addRecentlyViewedMeal);

// Recently added to meal plans routes
router.get('/recently-added-to-meal-plans', auth.authWithTerms, userController.getRecentlyAddedToMealPlans);
router.post('/recently-added-to-meal-plans', auth.authWithTerms, userController.addRecentlyAddedToMealPlan);

// Get meals from saved meal plans for history
router.get('/meals-from-saved-plans', auth.authWithTerms, userController.getMealsFromSavedPlans);

router.get('/family-members', auth, userController.getFamilyMembers);
router.post('/family-members', auth, userController.addFamilyMember);

router.delete('/family-members/:memberId', auth, userController.removeFamilyMember);


module.exports = router;
